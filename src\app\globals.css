
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 
  The HSL variables below were part of the ShadCN theming.
  With the new roadmap focusing on Ant Design and a direct Tailwind color palette,
  these might be progressively phased out or adjusted.
  AntD theming is handled via ConfigProvider and its token/components configuration.
  Tailwind uses the colors defined directly in tailwind.config.ts.
  For now, retaining them to avoid breaking existing ShadCN components if they are still in use.
*/

@layer base {
  :root {
    /* Default light theme HSL variables (primarily for any existing ShadCN components) */
    --background: 0 0% 100%; /* white */
    --foreground: 222.2 84% 4.9%; /* near black */

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 221.2 83.2% 53.3%; /* Corresponds to #1677ff (AntD primary) */
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%; /* This should be #FAAD14 from new theme if mapped */
    --accent-foreground: 222.2 47.4% 11.2%;
    
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%; /* Primary color for focus rings */

    --radius: 0.5rem; /* Default ShadCN radius, new config uses 6px */
  }

  .dark {
    /* Dark theme HSL variables (primarily for any existing ShadCN components) */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%; /* Lighter blue for dark mode */
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-white text-dark-text; /* Using direct colors from new Tailwind config */
    /* font-family is set in layout.tsx via className="font-sans" */
  }
}

/* Basic styling for react-quill content if needed */
.ql-editor {
  min-height: 200px; /* Or any desired height */
  font-size: 1rem;
  line-height: 1.6;
}

.ql-editor p,
.ql-editor ol,
.ql-editor ul,
.ql-editor pre,
.ql-editor blockquote,
.ql-editor h1,
.ql-editor h2,
.ql-editor h3,
.ql-editor h4,
.ql-editor h5,
.ql-editor h6 {
  margin-bottom: 0.75em;
}

.ql-editor strong {
  font-weight: bold;
}
.ql-editor em {
  font-style: italic;
}
.ql-editor u {
  text-decoration: underline;
}
.ql-editor pre {
  background-color: #f5f5f5;
  border: 1px solid #ccc;
  padding: 0.5em;
  border-radius: 4px;
  white-space: pre-wrap;
  word-wrap: break-word;
}
.ql-editor blockquote {
  border-left: 4px solid #ccc;
  margin-bottom: 5px;
  margin-top: 5px;
  padding-left: 16px;
}
.ql-editor a {
  color: var(--ant-primary-color, #1677ff); /* Use AntD primary color or fallback */
  text-decoration: underline;
}
/* Adjustments for Ant Design modals if toolbar overflows */
.ant-modal-body .ql-toolbar {
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 10; /* Ensure toolbar is above content */
  border-bottom: 1px solid #d9d9d9;
}
